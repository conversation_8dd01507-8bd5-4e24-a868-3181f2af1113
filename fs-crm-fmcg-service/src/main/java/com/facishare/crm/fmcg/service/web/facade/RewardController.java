package com.facishare.crm.fmcg.service.web.facade;

import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.exception.BizException;
import com.facishare.crm.fmcg.common.adapter.exception.RewardFmcgException;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.api.rule.*;
import com.facishare.crm.fmcg.tpm.api.scan.*;
import com.facishare.crm.fmcg.tpm.reward.handler.StoreScanCodeRewardHandler;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IPhysicalRewardService;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IRewardRuleManager;
import com.facishare.crm.fmcg.tpm.web.service.ScanCodeService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IActivityRewardRuleService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Author: linmj
 * Date: 2023/9/18 15:43
 */
@RestController
@RequestMapping("/reward")
public class RewardController {

    @Resource
    private IActivityRewardRuleService activityRewardRuleService;

    @Resource
    private IRewardRuleManager rewardRuleManager;

    @Resource
    private ScanCodeService scanCodeService;

    @Resource
    private IPhysicalRewardService physicalRewardService;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private StoreScanCodeRewardHandler storeScanCodeRewardHandler;


    @RequestMapping("/add")
    public AddActivityRewardRule.Result add(@RequestBody AddActivityRewardRule.Arg arg) {
        arg.setTenantId(ApiContextManager.getContext().getTenantId());
        return activityRewardRuleService.add(arg);
    }

    @RequestMapping("/edit")
    public UpdateRewardRule.Result edit(@RequestBody UpdateRewardRule.Arg arg) {
        arg.setTenantId(ApiContextManager.getContext().getTenantId());
        return activityRewardRuleService.update(arg);
    }

    @RequestMapping("/get")
    public GetActivityRewardRule.Result get(@RequestBody GetActivityRewardRule.Arg arg) {
        arg.setTenantId(ApiContextManager.getContext().getTenantId());
        return activityRewardRuleService.get(arg);
    }

    @RequestMapping("get_rule_describe")
    public RewardDescribeDTO getRuleDescribe(@RequestBody GetRuleDescribe.Arg arg) {
        return rewardRuleManager.getRewardDescribe(ApiContextManager.getContext().getTenantId(), arg);
    }


    @RequestMapping("/{env}/scan_big_date_code")
    public BigDateScanCode.Result scanMnBigDate(@RequestBody BigDateScanCode.Arg arg) {
        return scanCodeService.bigDateScanCode(arg);
    }

    @RequestMapping("/{env}/big_date_pay")
    public BigDatePay.Result bigDatePay(@RequestBody BigDatePay.Arg arg) {
        return scanCodeService.bigDatePay(arg);
    }

    @RequestMapping("/{env}/close_big_date_wx_order")
    public CloseBigDateWxOrder.Result bigDatePay(@RequestBody CloseBigDateWxOrder.Arg arg) {
        return scanCodeService.closeBigDateWxOrder(arg);
    }

    @RequestMapping("/{environment}/consumer_reward_list")
    public ConsumerRewardList.Result consumerRewardList(@PathVariable String environment, @RequestBody ConsumerRewardList.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        arg.setToken(arg.getWxToken());
        arg.setSkipWxValidate(true);
        return physicalRewardService.consumerRewardList(arg);
    }

    @RequestMapping("/{environment}/get_physical_item_info")
    public GetPhysicalItemInfo.Result getPhysicalItemInfo(@PathVariable String environment, @RequestBody GetPhysicalItemInfo.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        arg.setToken(arg.getWxToken());
        arg.setSkipWxValidate(true);
        return physicalRewardService.getPhysicalItemInfo(arg);
    }

    @RequestMapping("/{environment}/physical_item_write_off")
    public PhysicalItemWriteOff.Result physicalItemWriteOff(@PathVariable String environment, @RequestBody PhysicalItemWriteOff.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        arg.setToken(arg.getWxToken());
        arg.setSkipWxValidate(true);
        return physicalRewardService.physicalItemWriteOff(arg);
    }

    @RequestMapping("/{environment}/fill_mail_info_for_physical_item")
    public FillMailInfoForPhysicalItem.Result fillMailInfoForPhysicalItem(@PathVariable String environment, @RequestBody FillMailInfoForPhysicalItem.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        arg.setToken(arg.getWxToken());
        arg.setSkipWxValidate(true);
        return physicalRewardService.fillMailInfoForPhysicalItem(arg);
    }

    @RequestMapping("/{environment}/query_write_off_qr_code_status")
    public QueryWriteOffQrCodeStatus.Result queryWriteOffQrCodeStatus(@PathVariable String environment, @RequestBody QueryWriteOffQrCodeStatus.Arg arg) {
        arg.setEnvironment(environment.toLowerCase());
        return physicalRewardService.queryWriteOffQrCodeStatus(arg);
    }

    @RequestMapping("/{environment}/return_value_for_physical_item")
    public String returnValueForPhysicalItem(@RequestBody JSONObject arg) {
        String tenantId = arg.getString("tenant_id");
        IObjectData record = serviceFacade.findObjectData(User.systemUser(tenantId), arg.getString("record_id"), ApiNames.POINTS_EXCHANGE_RECORD_OBJ);
        physicalRewardService.returnValueForPhysicalItem(tenantId, record);
        return "success";
    }

    @RequestMapping("/{environment}/storeScanCode")
    public StoreScanCode.Result storeScanCode(@RequestBody StoreScanCode.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        arg.setOuterTenantId(context.getOutTenantId());
        arg.setOuterUserId(context.getOutUserId());
        try {
            return scanCodeService.storeScanCode(arg);
        } catch (RewardFmcgException e) {
            throw new BizException(e.getMessage(),e.getErrorCode());
        }
    }

    @RequestMapping("/{environment}/storeReward")
    public StoreReward.Result storeReward(@RequestBody StoreReward.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        arg.setOuterTenantId(context.getOutTenantId());
        arg.setOuterUserId(context.getOutUserId());
        try {
            return storeScanCodeRewardHandler.handle(arg);
        } catch (RewardFmcgException e) {
            throw new BizException(e.getMessage(),e.getErrorCode());
        }
    }

}

package com.facishare.crm.fmcg.service.web.inner;

import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.exception.BizException;
import com.facishare.crm.fmcg.common.adapter.exception.RewardFmcgException;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.mengniu.handler.NewRewardService;
import com.facishare.crm.fmcg.mengniu.handler.RedPacketEventDistributor;
import com.facishare.crm.fmcg.service.web.inner.provider.InnerApiResult;
import com.facishare.crm.fmcg.tpm.api.scan.StoreReward;
import com.facishare.crm.fmcg.tpm.api.scan.StoreScanCode;
import com.facishare.crm.fmcg.tpm.business.abstraction.IPresetRedPacketService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRedPacketService;
import com.facishare.crm.fmcg.tpm.reward.dto.CustomerPublish;
import com.facishare.crm.fmcg.tpm.reward.dto.SelfDefineReward;
import com.facishare.crm.fmcg.tpm.reward.dto.StockCheckReward;
import com.facishare.crm.fmcg.tpm.reward.handler.BigDateHandler;
import com.facishare.crm.fmcg.tpm.reward.handler.SelfDefineRewardHandler;
import com.facishare.crm.fmcg.tpm.reward.handler.StockCheckRewardHandler;
import com.facishare.crm.fmcg.tpm.reward.handler.StoreScanCodeRewardHandler;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IScanCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.Serializable;

/**
 * Author: linmj
 * Date: 2023/10/23 15:03
 */

@Slf4j
@RestController
@RequestMapping(value = "/inner/Reward", produces = "application/json")
public class InnerRewardController {

    @Resource
    private IRedPacketService redPacketService;

    @Resource
    private BigDateHandler bigDateHandler;

    @Resource
    private IPresetRedPacketService presetRedPacketService;

    @Resource
    private RedPacketEventDistributor redPacketEventDistributor;

    @Resource
    private SelfDefineRewardHandler selfDefineRewardHandler;

    @Resource
    private StockCheckRewardHandler stockCheckRewardHandler;

    @Resource
    private NewRewardService<Serializable> newRewardService;

    @Resource
    private StoreScanCodeRewardHandler storeScanCodeRewardHandler;

    @Resource
    private IScanCodeService scanCodeService;


    @RequestMapping(value = "/refreshRedPacketInfo")
    public InnerApiResult<String> refreshRedPacketInfo(@RequestBody JSONObject arg) {
        try {
            redPacketService.refreshRedPacketInfo(ApiContextManager.getContext().getTenantId(), arg.getString("redPacketId"));
        } catch (Exception e) {
            log.info("refreshRedPacketInfo error", e);
            return new InnerApiResult<>(500, e.getMessage());
        }
        return new InnerApiResult<>("success");
    }

    @RequestMapping(value = "/refreshRedPacketAccountInfo")
    public InnerApiResult<String> refreshRedPacketAccountInfo(@RequestBody JSONObject arg) {
        try {
            redPacketService.refreshAccountInfo(ApiContextManager.getContext().getTenantId(), arg.getString("redPacketId"));
        } catch (Exception e) {
            log.info("refreshRedPacketAccountInfo error", e);
            return new InnerApiResult<>(500, e.getMessage());
        }
        return new InnerApiResult<>("success");
    }

    @RequestMapping(value = "/bigDateReward")
    public InnerApiResult<String> bigDateReward(@RequestBody JSONObject arg) {
        try {
            bigDateHandler.handle(arg.getString("bizCodeId"));
        } catch (Exception e) {
            log.info("bigDateReward error", e);
            return new InnerApiResult<>(500, e.getMessage());
        }
        return new InnerApiResult<>("success");
    }

    @RequestMapping(value = "/PresetRedPacket")
    public InnerApiResult<String> presetRedPacket(@RequestBody JSONObject arg) {
        try {
            presetRedPacketService.presetRedPacket(arg.getString("tenant_id"), arg.getString("red_packet_ids"), arg.getString("event_type"));
        } catch (Exception e) {
            log.info("bigDateReward error", e);
            return new InnerApiResult<>(500, e.getMessage());
        }
        return new InnerApiResult<>("success");
    }

    @RequestMapping(value = "/redPacketAuthUserRemind")
    public InnerApiResult<String> redPacketAuthUserRemind(@RequestBody JSONObject arg) {
        try {
            redPacketEventDistributor.redPacketAuthUserRemind(ApiContextManager.getContext().getTenantId(), arg.getString("redPacketId"), arg.getString("apiName"));
        } catch (Exception e) {
            log.info("bigDateReward error", e);
            return new InnerApiResult<>(500, e.getMessage());
        }
        return new InnerApiResult<>("success");
    }

    @RequestMapping(value = "/selfDefineReward")
    public InnerApiResult<SelfDefineReward.Result> selfDefineReward(@RequestBody SelfDefineReward.Arg arg) {
        try {
            return new InnerApiResult<>(selfDefineRewardHandler.handle(arg));
        } catch (Exception e) {
            log.info("selfDefineReward error", e);
            return new InnerApiResult<>(500, e instanceof BizException ? ((BizException) e).getFailureMessage() : e.getMessage());
        }
    }

    @RequestMapping(value = "/stockCheckReward")
    public InnerApiResult<StockCheckReward.Result> stockCheckReward(@RequestBody StockCheckReward.Arg arg) {
        try {
            return new InnerApiResult<>(stockCheckRewardHandler.handle(arg));
        } catch (Exception e) {
            log.info("stockCheckReward error", e);
            return new InnerApiResult<>(500, e instanceof BizException ? ((BizException) e).getFailureMessage() : e.getMessage());
        }
    }

    @RequestMapping(value = "/CustomerPublishRedPacket")
    public InnerApiResult<CustomerPublish.Result> customerPublishRedPacket(@RequestBody CustomerPublish.Arg arg) {
        try {
            return new InnerApiResult<>(newRewardService.customerPublishRedPacket(arg));
        } catch (Exception e) {
            log.info("customPublishRedPacket error", e);
            return new InnerApiResult<>(500, e instanceof BizException ? ((BizException) e).getFailureMessage() : e.getMessage());
        }
    }

    @RequestMapping(value = "/storeScanCode")
    public InnerApiResult<StoreScanCode.Result> storeScanCode(@RequestBody StoreScanCode.Arg arg) {
        try {
            return new InnerApiResult<>(scanCodeService.storeScanCode(arg));
        } catch (Exception e) {
            log.info("storeScanCode error", e);
            if (e instanceof BizException) {
                return new InnerApiResult<>(((BizException) e).getFailureCode(), ((BizException) e).getFailureMessage());
            }
            return new InnerApiResult<>(50010, e.getMessage());
        }
    }

    @RequestMapping(value = "/storeReward")
    public InnerApiResult<StoreReward.Result> storeReward(@RequestBody StoreReward.Arg arg) {
        try {
            return new InnerApiResult<>(storeScanCodeRewardHandler.handle(arg));
        } catch (RewardFmcgException e) {
            return new InnerApiResult<>(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            log.info("storeReward error", e);
            if (e instanceof BizException) {
                return new InnerApiResult<>(((BizException) e).getFailureCode(), ((BizException) e).getFailureMessage());
            }
            return new InnerApiResult<>(50010, e.getMessage());
        }
    }
}

<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee http://xmlns.jcp.org/xml/ns/javaee/web-app_3_1.xsd"
         version="3.1"
         metadata-complete="true">

    <display-name>fs-crm-fmcg-service</display-name>

    <!-- 解决i18n servlet重复定义问题 -->
    <absolute-ordering>
        <others/>
    </absolute-ordering>

    <context-param>
        <param-name>contextConfigLocation</param-name>
        <param-value>classpath:application-context.xml</param-value>
    </context-param>

    <context-param>
        <param-name>fcpServerPort</param-name>
        <param-value>12014</param-value>
    </context-param>

    <context-param>
        <param-name>enableFcpFile</param-name>
        <param-value>true</param-value>
    </context-param>

    <context-param>
        <param-name>resteasy.servlet.mapping.prefix</param-name>
        <param-value>/API</param-value>
    </context-param>

    <listener>
        <listener-class>
            org.jboss.resteasy.plugins.server.servlet.ResteasyBootstrap
        </listener-class>
    </listener>

    <listener>
        <listener-class>
            com.facishare.paas.appframework.fcp.model.FcpRestServerContextListener
        </listener-class>
    </listener>

    <servlet>
        <servlet-name>Resteasy</servlet-name>
        <servlet-class>
            org.jboss.resteasy.plugins.server.servlet.HttpServletDispatcher
        </servlet-class>
    </servlet>

    <servlet-mapping>
        <servlet-name>Resteasy</servlet-name>
        <url-pattern>/API/*</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>dispatcher-facade</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <init-param>
            <param-name>contextConfigLocation</param-name>
            <param-value>classpath:mvc-facade.xml</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <servlet>
        <servlet-name>dispatcher-inner</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <init-param>
            <param-name>contextConfigLocation</param-name>
            <param-value>classpath:mvc-inner.xml</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet>
        <servlet-name>dispatcher-wechat</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <init-param>
            <param-name>contextConfigLocation</param-name>
            <param-value>classpath:mvc-wechat.xml</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <servlet>
        <servlet-name>dispatcher-fesco</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <init-param>
            <param-name>contextConfigLocation</param-name>
            <param-value>classpath:mvc-fesco.xml</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <servlet>
        <servlet-name>dispatcher-rewards-platform</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <init-param>
            <param-name>contextConfigLocation</param-name>
            <param-value>classpath:mvc-rewards-platform.xml</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <servlet-mapping>
        <servlet-name>dispatcher-facade</servlet-name>
        <url-pattern>/FMCG/API/*</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>dispatcher-inner</servlet-name>
        <url-pattern>/FMCG/InnerAPI/*</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>dispatcher-fesco</servlet-name>
        <url-pattern>/fesco/*</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>dispatcher-rewards-platform</servlet-name>
        <url-pattern>/rewards_platform/*</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
        <servlet-name>dispatcher-wechat</servlet-name>
        <url-pattern>/FMCG/WeChatAPI/*</url-pattern>
    </servlet-mapping>

    <welcome-file-list>
        <welcome-file>index.html</welcome-file>
    </welcome-file-list>
</web-app>

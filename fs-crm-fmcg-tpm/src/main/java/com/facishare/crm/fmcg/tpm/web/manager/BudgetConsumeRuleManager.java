package com.facishare.crm.fmcg.tpm.web.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.beust.jcommander.internal.Sets;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.utils.LanguageReplaceWrapper;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetNewConsumeRuleDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.tpm.service.abstraction.BudgetConsumeFlowService;
import com.facishare.crm.fmcg.tpm.service.abstraction.PluginInstanceService;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.condition.ConditionAdapter;
import com.facishare.crm.fmcg.tpm.web.condition.model.ConditionDto;
import com.facishare.crm.fmcg.tpm.web.contract.model.*;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IBudgetConsumeRuleManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldLayoutPojo;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.auth.model.AuthContext;
import com.facishare.paas.auth.model.FunctionPojo;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.UdefButton;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fxiaoke.paas.auth.factory.FuncClient;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/1 下午4:20
 */
@Service
@Slf4j
@SuppressWarnings("Duplicates")
public class BudgetConsumeRuleManager implements IBudgetConsumeRuleManager {

    @Resource
    private BudgetNewConsumeRuleDAO budgetNewConsumeRuleDAO;

    @Resource
    private ConditionAdapter conditionAdapter;

    @Resource
    private PluginInstanceService pluginService;

    @Resource
    private BudgetConsumeFlowService budgetConsumeFlowService;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private FuncClient funcClient;

    private static final Set<String> ALLOW_API_NAME = new HashSet<>();
    private static final Map<String, List<String>> API_NAME_MAP = new HashMap<>();
    private static final String CONFIG_NAME = "fs-fmcg-tpm-config";

    private static final String BUDGET_CLOSURE_BUTTON_API_NAME = "BudgetClosure_button_default";
    private static final String CLOSED_STATUS_FIELD_API_NAME = "closed_status";
    private static final String CLOSE_TIME_FIELD_API_NAME = "close_time";
    private static final String DETAIL_LAYOUT_TYPE = "detail";

    private static final Set<String> VALIDATE_API_NAME = new HashSet<>();
    private static final String PLUGIN_NAME = "budget_consume_new_mode";

    // 使用静态初始化块初始化集合
    static {
        // 初始化 ALLOW_API_NAME
        ALLOW_API_NAME.add(ApiNames.TPM_ACTIVITY_OBJ);
        ALLOW_API_NAME.add(ApiNames.TPM_DEALER_ACTIVITY_COST);
        ALLOW_API_NAME.add(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
        ALLOW_API_NAME.add(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        ALLOW_API_NAME.add(ApiNames.TPM_ACTIVITY_PROOF_OBJ);
        ALLOW_API_NAME.add(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ);
        ALLOW_API_NAME.add(ApiNames.EXPENSE_CLAIM_FORM_OBJ);
        ALLOW_API_NAME.add("PromotionActivityObj");
        ALLOW_API_NAME.add("SalesOrderObj");

        // 配置监听
        ConfigFactory.getConfig(CONFIG_NAME, config -> {
            String json = config.get("BUDGET_CONSUME_RULE_FILTER_OBJ");
            if (!Strings.isNullOrEmpty(json)) {
                Map<String, List<String>> tempMap = JSON.parseObject(json,
                        new TypeReference<Map<String, List<String>>>() {
                        });
                API_NAME_MAP.putAll(tempMap);  // 使用putAll更新Map内容
            }
        });

        // 初始化 VALIDATE_API_NAME
        VALIDATE_API_NAME.add(ApiNames.TPM_ACTIVITY_OBJ);
        VALIDATE_API_NAME.add(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
    }


    @Override
    public void consumeRuleInfoValidate(String tenantId, BudgetNewConsumeRuleVO budgetNewConsumeRuleVO) {

        //校验必填项
        consumeRuleRequiredValidate(budgetNewConsumeRuleVO);
        //规则重名校验
        consumeRuleDuplicateNameValidate(tenantId, budgetNewConsumeRuleVO.getName());
        //校验所有业务对象，是否有启动的消费规则 业务对象 + 业务类型
        //根据规则的规则类型，如果是冻结扣减，并且扣减的是其他对象，则该对象也要校验唯一启用。
        objectExistsConsumeRuleValidate(tenantId, budgetNewConsumeRuleVO.getApiName(), budgetNewConsumeRuleVO.getRecordType(), ConsumeRuleType.FREEZE);
        if (ConsumeRuleType.FREEZE_DEDUCTION.value().equals(budgetNewConsumeRuleVO.getRuleType())
                && ConsumeDeductType.DEDUCT_OTHER.value().equals(budgetNewConsumeRuleVO.getDeductType())) {
            objectExistsConsumeRuleValidate(tenantId, budgetNewConsumeRuleVO.getDeductApiName(), budgetNewConsumeRuleVO.getDeductRecordType(), ConsumeRuleType.DEDUCTION);
        }
        // 校验释放对象
        budgetReleaseValidate(tenantId, budgetNewConsumeRuleVO);
        // 校验预提，不支持直接扣减
        budgetPrepaidValidate(budgetNewConsumeRuleVO);

        //根据不同选择预算表的方式校验
        String budgetMethod = budgetNewConsumeRuleVO.getBudgetMethod();
        if (BudgetMethodEnum.AUTOMATIC.value().equals(budgetMethod)) {
            //校验消费规则的 预算分摊比例等于100%
            consumeRuleBudgetRatioValidate(budgetNewConsumeRuleVO.getBudgetTableAutomaticNodes());
            //所选模版相同，费用预算表映射不能完全一致
            budgetTemplateRelationValidate(budgetNewConsumeRuleVO.getBudgetTableAutomaticNodes());
        } else if (BudgetMethodEnum.MANUAL.value().equals(budgetMethod)) {
            //手动配置选择预算表，不能完全一致。
            budgetTableManualValidate(budgetNewConsumeRuleVO.getBudgetTableManualNodes());
        }

    }

    private void budgetPrepaidValidate(BudgetNewConsumeRuleVO budgetNewConsumeRuleVO) {

        if (ConsumeConfigType.PROVISION_DISABLE.value().equals(budgetNewConsumeRuleVO.getProvisionStatus())) {
            return;
        }
        if (ConsumeRuleType.DEDUCTION.value().equals(budgetNewConsumeRuleVO.getRuleType())) {
            // 消费规则预提不支持直接扣减
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_RULE_RELEASE_MANAGER_3));
        }
        if (BudgetMethodEnum.AUTOMATIC.value().equals(budgetNewConsumeRuleVO.getBudgetMethod())) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_RULE_NO_AUTOMATIC_MANAGER_0));
        }

    }

    private void budgetReleaseValidate(String tenantId, BudgetNewConsumeRuleVO budgetNewConsumeRuleVO) {
        // 没有配置释放对象，不需要校验
        if (ConsumeConfigType.RELEASE_DISABLE.value().equals(budgetNewConsumeRuleVO.getReleaseStatus())) {
            return;
        }
        if (BudgetMethodEnum.AUTOMATIC.value().equals(budgetNewConsumeRuleVO.getBudgetMethod())) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_RULE_NO_AUTOMATIC_MANAGER_0));
        }
        List<ReleaseBusinessVO> releaseList = budgetNewConsumeRuleVO.getReleaseList();
        if (CollectionUtils.isNotEmpty(releaseList)) {
            if (releaseList.size() > 3) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_RULE_RELEASE_MANAGER_1));
            }
            releaseList.forEach(releaseNodeVO -> {
                //校验释放对象，是否有启动的消费规则
                objectExistsConsumeRuleValidate(tenantId, releaseNodeVO.getReleaseApiName(), releaseNodeVO.getReleaseRecordType(), ConsumeRuleType.RELEASE);
                if (ConsumeRuleType.FREEZE_DEDUCTION.value().equals(budgetNewConsumeRuleVO.getRuleType())
                        && ConsumeDeductType.DEDUCT_OTHER.value().equals(budgetNewConsumeRuleVO.getDeductType())) {
                    if (releaseNodeVO.getReleaseApiName().equals(budgetNewConsumeRuleVO.getDeductApiName())) {
                        throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_RULE_RELEASE_MANAGER_2));
                    }
                }
            });
        }
    }

    private void budgetTableManualValidate(List<BudgetTableManualNodeVO> budgetTableManualNodes) {

        Map<String, BudgetTableFrozenVO> frozenVOMap = Maps.newHashMap();
        Map<String, BudgetTableDeductVO> deductVOMap = Maps.newHashMap();

        for (BudgetTableManualNodeVO budgetTableManualNode : budgetTableManualNodes) {
            BudgetTableFrozenVO budgetTableFrozen = budgetTableManualNode.getBudgetTableFrozen();
            BudgetTableDeductVO budgetTableDeDuct = budgetTableManualNode.getBudgetTableDeDuct();

            if (frozenVOMap.containsKey(budgetTableFrozen.getApiName())) {
                BudgetTableFrozenVO tableFrozenVO = frozenVOMap.get(budgetTableFrozen.getApiName());
                if (budgetTableFrozen.getRelationField().equals(tableFrozenVO.getRelationField())
                        || budgetTableFrozen.getAmountField().equals(tableFrozenVO.getAmountField())) {
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_RULE_MANUAL_BUDGET_RELATION_ERROR));
                }
            }
            if (deductVOMap.containsKey(budgetTableDeDuct.getApiName())) {
                BudgetTableDeductVO tableDeductVO = deductVOMap.get(budgetTableDeDuct.getApiName());
                if (budgetTableDeDuct.getAmountField().equals(tableDeductVO.getAmountField())) {
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_RULE_MANUAL_BUDGET_RELATION_ERROR));
                }
            }
            if (StringUtils.isNotEmpty(budgetTableFrozen.getApiName())) {
                frozenVOMap.put(budgetTableFrozen.getApiName(), budgetTableFrozen);
            }
            if (StringUtils.isNotEmpty(budgetTableDeDuct.getApiName())) {
                deductVOMap.put(budgetTableDeDuct.getApiName(), budgetTableDeDuct);
            }

        }
    }

    @Override
    public void consumeRuleEditValidate(String tenantId, BudgetNewConsumeRuleVO budgetNewConsumeRuleVO, BudgetNewConsumeRulePO oldPO) {

        //校验必填项
        consumeRuleRequiredValidate(budgetNewConsumeRuleVO);

        if (!oldPO.getName().equals(budgetNewConsumeRuleVO.getName())) {
            //规则重名校验
            consumeRuleDuplicateNameValidate(tenantId, budgetNewConsumeRuleVO.getName());
        }
        if (!oldPO.getApiName().equals(budgetNewConsumeRuleVO.getApiName())
                || !oldPO.getRecordType().equals(budgetNewConsumeRuleVO.getRecordType())) {
            //校验所有业务对象，是否有启动的消费规则 业务对象 + 业务类型
            objectExistsConsumeRuleValidate(tenantId, budgetNewConsumeRuleVO.getApiName(), budgetNewConsumeRuleVO.getRecordType(), ConsumeRuleType.FREEZE);
        }
        if (!oldPO.getDeductApiName().equals(budgetNewConsumeRuleVO.getDeductApiName())
                || !oldPO.getDeductRecordType().equals(budgetNewConsumeRuleVO.getDeductRecordType())) {

            //根据规则的规则类型，如果是冻结扣减，并且扣减的是其他对象，则该对象也要校验唯一启用。
            if (ConsumeRuleType.FREEZE_DEDUCTION.value().equals(budgetNewConsumeRuleVO.getRuleType())
                    && ConsumeDeductType.DEDUCT_OTHER.value().equals(budgetNewConsumeRuleVO.getDeductType())) {
                objectExistsConsumeRuleValidate(tenantId, budgetNewConsumeRuleVO.getDeductApiName(), budgetNewConsumeRuleVO.getDeductRecordType(), ConsumeRuleType.DEDUCTION);
            }
        }
        // 校验释放对象
        budgetReleaseValidate(tenantId, budgetNewConsumeRuleVO);
        // 校验预提，不支持直接扣减
        budgetPrepaidValidate(budgetNewConsumeRuleVO);
        //根据不同选择预算表的方式校验
        String budgetMethod = budgetNewConsumeRuleVO.getBudgetMethod();
        if (BudgetMethodEnum.AUTOMATIC.value().equals(budgetMethod)) {
            //校验消费规则的 预算分摊比例等于100%
            consumeRuleBudgetRatioValidate(budgetNewConsumeRuleVO.getBudgetTableAutomaticNodes());
            //所选模版相同，费用预算表映射不能完全一致
            budgetTemplateRelationValidate(budgetNewConsumeRuleVO.getBudgetTableAutomaticNodes());
        } else if (BudgetMethodEnum.MANUAL.value().equals(budgetMethod)) {
            //手动配置选择预算表，不能完全一致。
            budgetTableManualValidate(budgetNewConsumeRuleVO.getBudgetTableManualNodes());
        }

    }


    @Override
    public void enclosureConditionFilter(String tenantId, Integer employeeId, BudgetNewConsumeRuleVO budgetNewConsumeRuleVO) {
        //扣减对象默认是消费对象
        String deductApiName = budgetNewConsumeRuleVO.getApiName();
        if (ConsumeRuleType.FREEZE_DEDUCTION.value().equals(budgetNewConsumeRuleVO.getRuleType())
                && ConsumeDeductType.DEDUCT_OTHER.value().equals(budgetNewConsumeRuleVO.getDeductType())) {
            //选择其他扣减时，为所选扣减对象
            deductApiName = budgetNewConsumeRuleVO.getDeductApiName();
        }
        List<BudgetRuleTypeNodeVO> ruleTypeNodes = budgetNewConsumeRuleVO.getRuleTypeNodes();
        for (BudgetRuleTypeNodeVO budgetRuleTypeNodeVO : ruleTypeNodes) {
            if (ConsumeRuleType.DEDUCTION.value().equals(budgetRuleTypeNodeVO.getType())) {
                fillConditionCode(tenantId, employeeId, budgetRuleTypeNodeVO, deductApiName);
            }
            if (ConsumeRuleType.FREEZE.value().equals(budgetRuleTypeNodeVO.getType())) {
                fillConditionCode(tenantId, employeeId, budgetRuleTypeNodeVO, budgetNewConsumeRuleVO.getApiName());
            }
            if (ConsumeRuleType.RELEASE.value().equals(budgetRuleTypeNodeVO.getType())) {
                List<ReleaseNodeVO> releaseNode = budgetRuleTypeNodeVO.getReleaseNode();
                for (ReleaseNodeVO releaseNodeVO : releaseNode) {
                    fillReleaseConditionCode(tenantId, employeeId, releaseNodeVO, releaseNodeVO.getReleaseApiName());
                }
            }
        }
    }

    private void fillConditionCode(String tenantId, Integer employeeId, BudgetRuleTypeNodeVO budgetRuleTypeNodeVO, String apiName) {
        String conditionCode = buildConditionCode(tenantId, employeeId, apiName, budgetRuleTypeNodeVO.getConditionCode(), budgetRuleTypeNodeVO.getWhereConditions());
        budgetRuleTypeNodeVO.setConditionCode(conditionCode);
    }

    private void fillReleaseConditionCode(String tenantId, Integer employeeId, ReleaseNodeVO releaseNodeVO, String apiName) {
        String conditionCode = buildConditionCode(tenantId, employeeId, apiName, releaseNodeVO.getConditionCode(), releaseNodeVO.getWhereConditions());
        releaseNodeVO.setConditionCode(conditionCode);
    }

    private String buildConditionCode(String tenantId, Integer employeeId, String apiName, String ruleCode, List<BudgetWhereConditionVO> whereConditions) {
        List<ConditionDto> conditionDtoList = new ArrayList<>();
        int i = 0;
        // (0 and 1) or (2 and 3)
        StringBuilder pattern = new StringBuilder();
        for (BudgetWhereConditionVO whereCondition : whereConditions) {
            if (StringUtils.isNotEmpty(pattern.toString())) {
                pattern.append(" or ");
            }
            List<String> rowList = new ArrayList<>();
            for (BudgetTriggerConditionVO triggerCondition : whereCondition.getTriggerConditions()) {
                ConditionDto conditionDto = new ConditionDto();
                conditionDto.setFieldName(triggerCondition.getFieldName());
                conditionDto.setValues(triggerCondition.getFieldValues());
                conditionDto.setOperator(triggerCondition.getOperator());
                conditionDto.setRowNo(i++);
                conditionDtoList.add(conditionDto);
                rowList.add(String.valueOf(conditionDto.getRowNo()));
            }
            if (CollectionUtils.isEmpty(rowList)) {
                continue;
            }
            String order;
            if (rowList.size() > 1) {
                order = String.join(" and ", rowList);
            } else {
                order = rowList.get(0);
            }
            pattern.append("(").append(order).append(")");
        }
        if (CollectionUtils.isEmpty(conditionDtoList) && StringUtils.isEmpty(pattern.toString())) {
            return null;
        }
        if (StringUtils.isEmpty(ruleCode)) {
            //设置 生成条件的code
            return conditionAdapter.publish(Integer.valueOf(tenantId), employeeId, apiName, pattern.toString(), conditionDtoList);
        } else {
            // RULE UPDATE
            conditionAdapter.update(Integer.valueOf(tenantId), employeeId, apiName, ruleCode, pattern.toString(), conditionDtoList);
            return ruleCode;
        }

    }


    @Override
    public void bindObjectPluginInstance(String tenantId, Integer employeeId, String apiName, String pluginName) {

        //查询对象是否已绑定插件
        if (pluginService.findPluginUnit(tenantId, apiName, pluginName)) {
            return;
        }
        try {
            pluginService.addPluginUnit(Integer.valueOf(tenantId), employeeId, apiName, pluginName);

        } catch (Exception e) {
            log.error("add plugin instance fail, e:", e);
        }
    }

    @Override
    public void deleteObjectPluginInstance(String tenantId, String apiName, String recordType, String pluginName) {
        //查询对象是否有启用的规则。
        if (!budgetNewConsumeRuleDAO.findByApiName(tenantId, apiName)) {
            //没有依据对象了，
            pluginService.deletePluginUnit(tenantId, apiName, pluginName);
        }
    }

    @Override
    public Integer deleteUseLessPlugin(String tenantId, String pluginName) {
        return pluginService.deleteUseLessByPluginName(tenantId, pluginName);
    }

    @Override
    public void addClosureComponents(String tenantId, Integer userId, String objectApiName) {
        IUdefButton old = serviceFacade.findButtonByApiName(User.systemUser(tenantId), BUDGET_CLOSURE_BUTTON_API_NAME, objectApiName);

        if (Objects.isNull(old)) {
            IUdefButton button = new UdefButton();
            button.setTenantId(tenantId);
            button.setDescribeApiName(objectApiName);
            button.setApiName(BUDGET_CLOSURE_BUTTON_API_NAME);
            button.setLabel("结案");//ignorei18n
            button.setDefineType("system");
            button.setButtonType("common");

            Wheres wheres = new Wheres();
            IFilter filter = new Filter();
            filter.setFieldName(TPMActivityFields.CLOSE_STATUS);
            filter.setOperator(Operator.EQ);
            filter.setFieldValues(Lists.newArrayList(TPMActivityFields.CLOSE_STATUS__UNCLOSED));
            wheres.setFilters(Lists.newArrayList(filter));
            button.setWheres(Lists.newArrayList(wheres));

            button.setIsActive(true);
            button.setDeleted(false);
            button.setUsePages(Lists.newArrayList("detail", "list"));
            serviceFacade.createCustomButton(User.systemUser(tenantId), button);

            AuthContext authContext = AuthContext.builder().userId("-10000").tenantId(tenantId).appId("CRM").build();
            String functionCode = String.format("%s||BudgetClosure", objectApiName);
            FunctionPojo function = new FunctionPojo();
            function.setAppId("CRM");
            function.setParentCode("00000000000000000000000000000000");
            function.setTenantId(tenantId);
            function.setFuncName("结案");//ignorei18n
            function.setFuncCode(functionCode);
            function.setFuncType(0);
            function.setIsEnabled(true);
            funcClient.addFunc(authContext, Lists.newArrayList(function));

            Set<String> addFunctionCodes = Sets.newHashSet();
            addFunctionCodes.add(functionCode);
            funcClient.updateRoleModifiedFuncPermission(authContext, "00000000000000000000000000000006", addFunctionCodes, Sets.newHashSet());
        }

        ObjectDescribeExt describe = ObjectDescribeExt.of(serviceFacade.findObject(tenantId, objectApiName));
        if (!describe.containsField(CLOSED_STATUS_FIELD_API_NAME)) {
            addField(tenantId, objectApiName, CLOSED_STATUS_FIELD_API_NAME);
        }
        if (!describe.containsField(CLOSE_TIME_FIELD_API_NAME)) {
            addField(tenantId, objectApiName, CLOSE_TIME_FIELD_API_NAME);
        }
    }

    private void addField(String tenantId, String objectApiName, String fieldApiName) {
        User superUser = User.systemUser(tenantId);
        String fieldDescribeJson = loadFieldDescribeJsonFromResource(fieldApiName);

        JSONObject field = JSON.parseObject(fieldDescribeJson);

        FieldLayoutPojo fieldLayout = new FieldLayoutPojo();
        ILayout layout = serviceFacade.getLayoutLogicService().findDefaultLayout(superUser, DETAIL_LAYOUT_TYPE, objectApiName);

        fieldLayout.setApiName(layout.getName());
        fieldLayout.setLabel(field.getString("label"));
        fieldLayout.setRenderType(field.getString("type"));
        fieldLayout.setReadonly(Boolean.TRUE.equals(field.getBoolean("is_readonly")));
        fieldLayout.setRequired(false);
        fieldLayout.setShow(true);
        fieldLayout.setLayoutType(DETAIL_LAYOUT_TYPE);

        LanguageReplaceWrapper.doInChinese(() -> {
            serviceFacade.addDescribeCustomField(superUser,
                    objectApiName,
                    fieldDescribeJson,
                    Lists.newArrayList(fieldLayout),
                    Lists.newArrayList());
        });
    }

    private String loadFieldDescribeJsonFromResource(String fieldApiName) {
        try {
            File file = ResourceUtils.getFile(String.format("classpath:tpm/module_fields/UDOBJ.%s.json", fieldApiName));
            return new String(Files.readAllBytes(file.toPath()));
        } catch (IOException e) {
            throw new MetaDataBusinessException("read field describe from file cause io exception.");
        }
    }

    @Override
    public Map<String, String> findObjectsByTenantId(String tenantId) {
        List<IObjectDescribe> describes = serviceFacade.findObjectsByTenantId(tenantId, false, true, true, true);
        Set<String> allowApiName = new HashSet<>(ALLOW_API_NAME);
        if (API_NAME_MAP.containsKey(tenantId)) {
            allowApiName.addAll(API_NAME_MAP.get(tenantId));
        }
        return describes.stream()
                .filter(describe -> describe.getApiName().endsWith("__c") || allowApiName.contains(describe.getApiName()))
                .collect(Collectors.toMap(IObjectDescribe::getApiName, IObjectDescribe::getDisplayName));
    }


    @Override
    public Map<String, String> getReleaseObjectLabels(String tenantId) {
        List<String> releaseObject = Lists.newArrayList(ApiNames.TPM_STORE_WRITE_OFF_OBJ);
        Map<String, IObjectDescribe> objects = serviceFacade.findObjects(tenantId, releaseObject);
        return objects.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getDisplayName()));
    }

    @Override
    public void addBudgetProvisionField(String tenantId, BudgetNewConsumeRulePO budgetNewConsumeRulePO) {
        List<BudgetTableManualNodeEntity> budgetTableManualNodes = budgetNewConsumeRulePO.getBudgetTableManualNodes();
        List<String> fields = new ArrayList<>();
        ObjectDescribeExt describe = ObjectDescribeExt.of(serviceFacade.findObject(tenantId, ApiNames.TPM_BUDGET_PROVISION_OBJ));
        for (BudgetTableManualNodeEntity budgetTableManualNode : budgetTableManualNodes) {
            BudgetTableFrozenEntity budgetTableFrozenEntity = budgetTableManualNode.getBudgetTableFrozenEntity();
            BudgetTableProvisionEntity budgetTableProvisionEntity = budgetTableFrozenEntity.getBudgetTableProvisionEntity();
            if (budgetTableProvisionEntity != null) {
                List<BudgetFieldRelationEntity> fieldRelation = budgetTableProvisionEntity.getFieldRelation();
                for (BudgetFieldRelationEntity budgetFieldRelationEntity : fieldRelation) {
                    if (!describe.containsField(budgetFieldRelationEntity.getSourceField())) {
                        fields.add(budgetFieldRelationEntity.getSourceField());
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(fields)) {
            for (String field : fields) {
                addCustomField(tenantId, field, describe);
            }
        }
    }

    private void addCustomField(String tenantId, String field, ObjectDescribeExt describe) {
        try {
            IFieldDescribe fieldDescribe = describe.getFieldDescribe(field);
            String fieldJson = JSON.toJSONString(fieldDescribe);
            JSONObject fieldDescribeJson = JSONObject.parseObject(fieldJson);
            JSONObject containerDocument = fieldDescribeJson.getJSONObject("containerDocument");
            containerDocument.remove("_id");

            FieldLayoutPojo fieldLayout = new FieldLayoutPojo();

            ILayout layout = serviceFacade.getLayoutLogicService().findDefaultLayout(User.systemUser(tenantId), DETAIL_LAYOUT_TYPE, ApiNames.TPM_BUDGET_PROVISION_OBJ);
            fieldLayout.setLayoutType(DETAIL_LAYOUT_TYPE);

            fieldLayout.setApiName(layout.getName());
            fieldLayout.setLabel(fieldDescribe.getLabel());
            fieldLayout.setRenderType(fieldDescribe.getType());
            fieldLayout.setRequired(fieldDescribe.isRequired());

            fieldLayout.setReadonly(Boolean.TRUE.equals(fieldDescribe.get("is_readonly", Boolean.class)));
            fieldLayout.setShow(true);
            fieldLayout.setRequired(false);
            fieldLayout.setLayoutType(DETAIL_LAYOUT_TYPE);

            LanguageReplaceWrapper.doInChinese(() -> {
                serviceFacade.addDescribeCustomField(User.systemUser(tenantId),
                        ApiNames.TPM_BUDGET_PROVISION_OBJ,
                        containerDocument.toString(),
                        Lists.newArrayList(fieldLayout),
                        Lists.newArrayList());
            });

        } catch (Exception e) {
            log.error("add custom field cause exception.", e);
        }

    }

    @Override
    public Map<String, Map<String, String>> getConsumeObjects(String tenantId) {
        List<String> consumeObjects = budgetNewConsumeRuleDAO.getConsumeObjects(tenantId);
        List<String> deDuctObjects = budgetNewConsumeRuleDAO.getDeDuctObjects(tenantId);
        return extConsumeRuleObjects(tenantId, consumeObjects, deDuctObjects);
    }

    private Map<String, Map<String, String>> extConsumeRuleObjects(String tenantId,
                                                                   List<String> consumeObjects,
                                                                   List<String> deDuctObjects) {

        Map<String, Map<String, String>> consumeObjectMap = Maps.newHashMap();
        List<String> objects = Lists.newArrayList();
        objects.addAll(consumeObjects);
        objects.addAll(deDuctObjects);
        Map<String, IObjectDescribe> describeMap = serviceFacade.findObjects(tenantId, objects);
        Map<String, String> consumeMap = new HashMap<>();
        for (String apiName : consumeObjects) {
            IObjectDescribe objectDescribe = describeMap.get(apiName);
            if (objectDescribe != null) {
                consumeMap.put(apiName, objectDescribe.getDisplayName());
            }
        }
        consumeObjectMap.put(BudgetNewConsumeRulePO.F_API_NAME, consumeMap);
        Map<String, String> deDuctMap = new HashMap<>();
        for (String apiName : deDuctObjects) {
            IObjectDescribe objectDescribe = describeMap.get(apiName);
            if (objectDescribe != null) {
                deDuctMap.put(apiName, objectDescribe.getDisplayName());
            }
        }
        consumeObjectMap.put(BudgetNewConsumeRulePO.F_DEDUCT_API_NAME, deDuctMap);
        return consumeObjectMap;
    }

    @Override
    public Boolean isProcessFlowCompleted(String tenantId, String ruleId) {
        return budgetConsumeFlowService.isExistedBudgetConsumeFlowData(tenantId, ruleId);
    }

    @Override
    public void existsEnableRuleValidate(String tenantId, BudgetNewConsumeRulePO budgetNewConsumeRulePO) {
        //根据规则的规则类型，如果是冻结扣减，并且扣减的是其他对象，则该对象也要校验唯一启用。
        objectExistsConsumeRuleValidate(tenantId, budgetNewConsumeRulePO.getApiName(), budgetNewConsumeRulePO.getRecordType(), ConsumeRuleType.FREEZE);
        String ruleType = budgetNewConsumeRulePO.getRuleType();
        Integer deductType = budgetNewConsumeRulePO.getDeductType();
        if (ConsumeRuleType.FREEZE_DEDUCTION.value().equals(ruleType)
                && ConsumeDeductType.DEDUCT_OTHER.value().equals(deductType)) {
            objectExistsConsumeRuleValidate(tenantId, budgetNewConsumeRulePO.getDeductApiName(), budgetNewConsumeRulePO.getDeductRecordType(), ConsumeRuleType.DEDUCTION);
        }
        // 没有配置释放对象，不需要校验
        if (ConsumeConfigType.RELEASE_ENABLE.value().equals(budgetNewConsumeRulePO.getReleaseStatus())) {
            budgetNewConsumeRulePO.getReleaseList().forEach(release -> {
                //校验释放对象，是否有启动的消费规则
                objectExistsConsumeRuleValidate(tenantId, release.getReleaseApiName(), release.getReleaseRecordType(), ConsumeRuleType.RELEASE);

            });
        }


    }

    @Override
    public void deleteConsumeRuleByTypeId(String tenantId, int employeeId, String budgetTypeId) {
        budgetNewConsumeRuleDAO.isUsedByConsumeBudgetType(tenantId, budgetTypeId).forEach(consumeRule -> {
            budgetNewConsumeRuleDAO.delete(tenantId, employeeId, consumeRule.getUniqueId());
            //判断规则依赖对象是否存在规则，无规则引用，去除依赖插件。
            deleteObjectPluginInstance(tenantId, consumeRule.getApiName(), consumeRule.getRecordType(), PLUGIN_NAME);
            if (ConsumeRuleType.FREEZE_DEDUCTION.value().equals(consumeRule.getRuleType())
                    && ConsumeDeductType.DEDUCT_OTHER.value().equals(consumeRule.getDeductType())) {
                deleteObjectPluginInstance(tenantId, consumeRule.getDeductApiName(), consumeRule.getDeductRecordType(), PLUGIN_NAME);
            }
        });
    }


    private void consumeRuleDuplicateNameValidate(String tenantId, String name) {
        if (budgetNewConsumeRuleDAO.isExistsByRuleName(tenantId, name)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_RULE_DUPLICATE_NAME_ERROR));
        }
    }

    private void consumeRuleBudgetRatioValidate(List<BudgetTableAutomaticNodeVO> budgetTableNodes) {
        try {
            // 不允许出现负 百分比
            List<BudgetTableAutomaticNodeVO> ruleNodeVOS = budgetTableNodes.stream()
                    .filter(v -> new BigDecimal(v.getRatio()).doubleValue() <= 0)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ruleNodeVOS)) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_RULE_MANAGER_0));
            }

            Optional<String> reduce = budgetTableNodes.stream()
                    .map(BudgetTableAutomaticNodeVO::getRatio)
                    .reduce((v1, v2) -> String.valueOf(new BigDecimal(v1).add(new BigDecimal(v2))));
            if (reduce.isPresent() && !reduce.get().equals("100")) {
                log.info("consumeRule reduce count is {}", reduce.get());
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_RULE_COST_RATIO_ERROR));
            }
        } catch (NumberFormatException formatException) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_RULE_MANAGER_1));
        }
    }

    private void budgetTemplateRelationValidate(List<BudgetTableAutomaticNodeVO> budgetTableNodes) {
        Map<String, List<BudgetFieldRelationVO>> templateMap = new HashMap<>();
        for (BudgetTableAutomaticNodeVO budgetTableNode : budgetTableNodes) {
            String key = String.format("%s-%s", budgetTableNode.getBudgetType(), budgetTableNode.getNodeId());
            if (templateMap.containsKey(key)) {
                validEqualsCollection(templateMap.get(key), budgetTableNode.getFieldRelation());
            } else {
                templateMap.put(key, budgetTableNode.getFieldRelation());
            }
        }
    }

    private void validEqualsCollection(List<BudgetFieldRelationVO> relationList,
                                       List<BudgetFieldRelationVO> nowRelationList) {
        if (CollectionUtils.isEqualCollection(relationList, nowRelationList)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_RULE_BUDGET_RELATION_ERROR));
        }
    }

    private void objectExistsConsumeRuleValidate(String tenantId, String apiName, String recordType, ConsumeRuleType consumeRuleType) {
        boolean isReleaseType = ConsumeRuleType.RELEASE.equals(consumeRuleType);
        boolean existsRule;

        if (VALIDATE_API_NAME.contains(apiName)) {
            // 活动类型校验
            existsRule = isReleaseType ?
                    budgetNewConsumeRuleDAO.isExistsReleaseByRecordType(tenantId, apiName, recordType) :
                    budgetNewConsumeRuleDAO.isExistsByRecordType(tenantId, apiName, recordType);
        } else {
            // 对象和业务类型校验 - 检查对象作为消费对象/扣减对象是否存在启用规则
            existsRule = isReleaseType ?
                    budgetNewConsumeRuleDAO.isExistsReleaseByApiNameWithRecordType(tenantId, apiName, recordType) :
                    budgetNewConsumeRuleDAO.isExistsByApiNameWithRecordType(tenantId, apiName, recordType);
        }

        if (existsRule) {
            throw new ValidateException(getErrorMessageByExistsMessage(tenantId, apiName));
        }
    }

    private String getErrorMessageByExistsMessage(String tenantId, String apiName) {
        IObjectDescribe objectDescribe = serviceFacade.findObject(tenantId, apiName);
        if (objectDescribe == null) {
            throw new ValidateException("not found object...");
        }

        String displayName = objectDescribe.getDisplayName();
        String messageKey = VALIDATE_API_NAME.contains(apiName) ?
                I18NKeys.BUDGET_CONSUME_RULE_NEW_ACTIVITY_TYPE_EXISTS_USED_ERROR :
                I18NKeys.BUDGET_CONSUME_RULE_NEW_OBJECT_EXISTS_USED_ERROR;

        return String.format(I18N.text(messageKey), displayName);
    }

    private void consumeRuleRequiredValidate(BudgetNewConsumeRuleVO vo) {
        // 全局必填项 校验
        validFieldEmpty(vo.getName(), vo.getApiName(), vo.getRecordType(), vo.getRuleType(), vo.getBudgetMethod());
        //节点必填项 校验
        validCollectionEmpty(vo.getRuleTypeNodes());
        if (BudgetMethodEnum.AUTOMATIC.value().equals(vo.getBudgetMethod())) {
            vo.getRuleTypeNodes().forEach(v -> validFieldEmpty(v.getType(), v.getTriggerTime(), v.getSourceField()));
            validCollectionEmpty(vo.getBudgetTableAutomaticNodes());
            for (BudgetTableAutomaticNodeVO automaticNode : vo.getBudgetTableAutomaticNodes()) {
                validFieldEmpty(automaticNode.getBudgetType(), automaticNode.getNodeId());
                validCollectionEmpty(automaticNode.getFieldRelation());
            }
        } else if (BudgetMethodEnum.MANUAL.value().equals(vo.getBudgetMethod())) {
            vo.getRuleTypeNodes().forEach(v -> validFieldEmpty(v.getType(), v.getTriggerTime()));
            validCollectionEmpty(vo.getBudgetTableManualNodes());
            for (BudgetTableManualNodeVO manualNode : vo.getBudgetTableManualNodes()) {
                // 如果是冻结扣减，则冻结的不能为空
                BudgetTableFrozenVO budgetTableFrozen = manualNode.getBudgetTableFrozen();
                BudgetTableDeductVO budgetTableDeDuct = manualNode.getBudgetTableDeDuct();
                validObjectEmpty(budgetTableFrozen, budgetTableDeDuct);
                if (ConsumeRuleType.FREEZE_DEDUCTION.value().equals(vo.getRuleType())) {
                    validFieldEmpty(budgetTableFrozen.getApiName(), budgetTableFrozen.getRelationField(), budgetTableFrozen.getAmountField());
                }
                if (!budgetTableDeDuct.getApiName().equals(budgetTableFrozen.getApiName())) {
                    validFieldEmpty(budgetTableDeDuct.getRelationField());
                }
                validFieldEmpty(budgetTableDeDuct.getApiName(), budgetTableDeDuct.getAmountField());
                // 判断是否开启预提,开启了预提必填校验
                if (ConsumeConfigType.PROVISION_ENABLE.value().equals(vo.getProvisionStatus())) {
                    validObjectEmpty(manualNode.getBudgetTableFrozen().getBudgetTableProvision());
                    BudgetTableProvisionVO budgetTableProvision = manualNode.getBudgetTableFrozen().getBudgetTableProvision();
                    validFieldEmpty(budgetTableProvision.getBudgetType(), budgetTableProvision.getBudgetLevel());
                    validCollectionEmpty(budgetTableProvision.getFieldRelation());
                }

            }
        }
    }


    /**
     * 校验字符串字段是否为空
     *
     * @param values 待校验的字符串数组
     */
    private void validFieldEmpty(String... values) {
        if (Arrays.stream(values).anyMatch(StringUtils::isEmpty)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_RULE_REQUIRED_ERROR));
        }
    }

    /**
     * 校验对象是否为空
     *
     * @param values 待校验的对象数组
     */
    private void validObjectEmpty(Object... values) {
        if (Arrays.stream(values).anyMatch(Objects::isNull)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_RULE_REQUIRED_ERROR));
        }
    }

    /**
     * 校验集合是否为空
     *
     * @param collection 待校验的集合
     * @param <T>        集合元素类型
     */
    private <T> void validCollectionEmpty(Collection<T> collection) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_RULE_REQUIRED_ERROR));
        }
    }

}

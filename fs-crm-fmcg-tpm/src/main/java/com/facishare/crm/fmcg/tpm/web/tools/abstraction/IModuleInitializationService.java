package com.facishare.crm.fmcg.tpm.web.tools.abstraction;

import com.facishare.crm.fmcg.tpm.web.contract.StoreWriteButtonInit;
import com.facishare.crm.fmcg.tpm.web.contract.UpdateActivityType;
import com.facishare.crm.fmcg.tpm.web.contract.UpdateRedPacketExpire;
import com.facishare.crm.fmcg.tpm.web.contract.kk.ActivityImport;

import java.util.List;
import java.util.Map;

/**
 * description :
 * service for module initialization
 * <p>
 * create by @yangqf
 * create time 2022/6/5 21:36
 */
public interface IModuleInitializationService {

    void addActivityTypeFields(String tenantId);

    void initAgreementStoreConfirmModule(String tenantId);

    void disableAgreementStoreConfirmModule(String tenantId);

    void upgradeFor810(String tenantId);

    void rollbackFor810(String tenantId);

    void disableSalesOrderConfirmModule(String tenantId);

    void initSalesOrderProductSalesTypeFields(String tenantId);

    void initSalesOrderTPMFields(String tenantId);

    void disableSalesOrderTPM810Fields(String tenantId);

    void initSalesOrderTPM815Fields(String tenantId);

    void initAllTenantSalesOrderTPMFields();

    void disableBudgetObjectDescribe(String tenantId);

    void initAllTenantSalesOrderTPM815Fields();

    void initAllTenantSalesOrderTPM815DeleteFields(String tenantId);

    void initOneMoreOrderModule(String tenantId);

    void initSalesOrderSalesModeFields(String tenantId);

    void doDisassembly(String tenantId, String objectDataIds, String flag);

    void initCashingFieldFor840(String tenantIds);

    String addStoreWriteOffButton(StoreWriteButtonInit.Arg arg);

    void addAllTenantDealerCostDealerWheres(String tenantId);

    void imgNPathToAPath(String tenantAccount, String nPaths);
    void kdhActivityImport(String tenantId, ActivityImport.Arg arg);

    void fmCashingType(String tenantId,String limitStr);

    void changeAccrualRuleData(String tenantId, Integer limit);

    void fixDisassemblyStatus(String tenantId, List<String> dataIds, String status);

    void fixBudgetAccountDetailTraceId(String tenantId, String id);

    void addObjFields(String tenantId, String objectApiName, String... fieldApiNames);

    void disableDeleteObjFields(String tenantId, String objectApiName, String... fieldApiNames);

    void fixActivityStatus(String tenantId, List<String> dataIds, String status);

    void fixHistoryData(String tenantId, List<String> dataIds, Map<String, Object> updateMap, String apiName);

    String updateActivityType(UpdateActivityType.Arg arg);

    void unFrozenForDisassemblyStatusFailed(String tenantId, List<String> dataIds);

    void doDisassemblyV2(String tenantId, List<String> dataIds);

    void updateRedPacketExpire(UpdateRedPacketExpire.Arg arg);

    void authUrl(String tenantId, String userId);

    void initDmsObjectFor950(String tenantIds);

    void initDmsObjectFor955(String tenantIds);
}
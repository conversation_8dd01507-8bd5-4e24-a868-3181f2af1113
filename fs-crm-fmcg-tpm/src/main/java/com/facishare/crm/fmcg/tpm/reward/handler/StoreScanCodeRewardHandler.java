package com.facishare.crm.fmcg.tpm.reward.handler;

import com.facishare.crm.fmcg.common.adapter.exception.RewardFmcgException;
import com.facishare.crm.fmcg.tpm.api.scan.StoreReward;
import com.facishare.crm.fmcg.tpm.reward.abstraction.ActivityRewardBase;
import com.facishare.crm.fmcg.tpm.reward.abstraction.ActivityRewardHandler;
import com.google.common.base.Strings;
import org.springframework.stereotype.Component;


@Component
public class StoreScanCodeRewardHandler extends ActivityRewardBase implements ActivityRewardHandler<StoreReward.Arg, StoreReward.Result> {


    @Override
    public StoreReward.Result handle(StoreReward.Arg arg) {
        if(Strings.isNullOrEmpty(arg.getCode())){
            throw new RewardFmcgException("123","有问题！");
        }
        return new StoreReward.Result();
    }
}
